import axios, { AxiosInstance } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { SearchParams, SearchResponse, Profile } from '../models/consumer.model';
import https from 'https';
import logger from '../config/logger';
import { config } from '../config';

// MoneyGram API response interfaces
interface MoneyGramProfile {
  name: {
    firstName: string;
    lastName: string;
  };
  address: {
    line1: string;
    city: string;
    countrySubdivisionCode: string;
    countryCode: string;
    postalCode: string;
  };
  mobilePhone: {
    number: string;
    countryDialCode: string;
  };
  rewardsNumber?: string;
  dateofBirth?: string;
  profileId: string;
}

interface MoneyGramSearchResponse {
  profiles: MoneyGramProfile[];
  paginationMetadata: {
    totalItems: number;
    currentPage: number;
    perPage: number;
    totalPages: number;
  };
}

export class MoneyGramConsumerService {
  private client: AxiosInstance;

  constructor() {
    // Configure axios with HTTPS agent for development
    const httpsAgent = process.env.NODE_ENV === 'development'
      ? new https.Agent({ rejectUnauthorized: false })
      : undefined;

    this.client = axios.create({
      baseURL: config.moneyGram.coreServiceUrl.replace(/\/$/, ''),
      httpsAgent,
      timeout: 10000
    });
  }

  // Helper method to construct and return the URL for debugging
  constructSearchUrl(params: SearchParams): string {
    const searchParams = new URLSearchParams();

    const partnerId = config.moneyGram.partnerId;
    searchParams.append('agentPartnerId', partnerId);
    searchParams.append('userLanguage', params.userLanguage || 'en-US');
    searchParams.append('maxProfilesToReturn', String(params.maxProfilesToReturn || 10));
    searchParams.append('pageNumber', String(params.pageNumber || 1));
    searchParams.append('perPage', String(params.perPage || 10));

    if (params.identificationType && params.identificationNumber) {
      const normalizedIdType = this.normalizeIdentificationType(params.identificationType);
      searchParams.append('identificationType', normalizedIdType);
      searchParams.append('identificationNumber', params.identificationNumber);
    }

    if (params.mobilePhone) {
      searchParams.append('mobilePhone', params.mobilePhone);
    }

    if (params.rewardsNumber) {
      searchParams.append('rewardsNumber', params.rewardsNumber);
    }

    const url = `/searchconsumerprofileservice/profiles/search?${searchParams.toString()}`;
    return `${this.client.defaults.baseURL}${url}`;
  }

  async searchProfiles(params: SearchParams): Promise<SearchResponse> {
    try {
      const searchParams = new URLSearchParams();

      // Required parameters
      const partnerId = config.moneyGram.partnerId;
      searchParams.append('agentPartnerId', partnerId);
      searchParams.append('userLanguage', params.userLanguage || 'en-US');
      searchParams.append('maxProfilesToReturn', String(params.maxProfilesToReturn || 10));
      searchParams.append('pageNumber', String(params.pageNumber || 1));
      searchParams.append('perPage', String(params.perPage || 10));

      // Search criteria - add based on what's provided
      if (params.identificationType && params.identificationNumber) {
        const normalizedIdType = this.normalizeIdentificationType(params.identificationType);
        searchParams.append('identificationType', normalizedIdType);
        searchParams.append('identificationNumber', params.identificationNumber);
      }

      if (params.mobilePhone) {
        searchParams.append('mobilePhone', params.mobilePhone);
      }

      if (params.rewardsNumber) {
        searchParams.append('rewardsNumber', params.rewardsNumber);
      }

      const url = `/searchconsumerprofileservice/profiles/search?${searchParams.toString()}`;
      const fullUrl = `${this.client.defaults.baseURL}${url}`;

      // Enhanced debugging - Complete request details
      console.log('=== MONEYGRAM CONSUMER SEARCH - COMPLETE REQUEST DEBUG ===');
      console.log('Timestamp:', new Date().toISOString());
      console.log('Original Input Parameters:', JSON.stringify(params, null, 2));
      console.log('Partner ID:', partnerId);
      console.log('Base URL:', this.client.defaults.baseURL);
      console.log('Relative URL:', url);
      console.log('Full URL:', fullUrl);
      console.log('Query Parameters Object:', Object.fromEntries(searchParams.entries()));
      console.log('Raw Query String:', searchParams.toString());
      console.log('Request Headers:', {
        'accept': 'application/json',
        'X-MG-ClientRequestId': uuidv4(),
        'X-MG-RequestId': uuidv4(),
        'Content-Type': 'application/json'
      });
      console.log('=== END REQUEST DEBUG ===\n');

      logger.info('=== MoneyGram Consumer Search Request ===', {
        url,
        fullUrl,
        params: Object.fromEntries(searchParams.entries()),
        rawQueryString: searchParams.toString(),
        originalParams: params,
        partnerId,
        baseURL: this.client.defaults.baseURL,
        timestamp: new Date().toISOString()
      });

      const response = await this.client.get<MoneyGramSearchResponse>(url, {
        headers: {
          'accept': 'application/json',
          'X-MG-ClientRequestId': uuidv4(),
          'X-MG-RequestId': uuidv4(),
          'Content-Type': 'application/json'
        }
      });

      // Enhanced debugging - Complete response details
      console.log('=== MONEYGRAM CONSUMER SEARCH - COMPLETE RESPONSE DEBUG ===');
      console.log('Timestamp:', new Date().toISOString());
      console.log('Response Status:', response.status);
      console.log('Response Status Text:', response.statusText);
      console.log('Response Headers:', JSON.stringify(response.headers, null, 2));
      console.log('Raw Response Data:', JSON.stringify(response.data, null, 2));
      console.log('Profile Count:', response.data.profiles?.length || 0);
      console.log('Pagination Metadata:', JSON.stringify(response.data.paginationMetadata, null, 2));
      console.log('=== END RESPONSE DEBUG ===\n');

      logger.info('=== MoneyGram Consumer Search Response ===', {
        status: response.status,
        profileCount: response.data.profiles?.length || 0,
        totalItems: response.data.paginationMetadata?.totalItems || 0,
        timestamp: new Date().toISOString()
      });

      // Transform MoneyGram response to our format
      const transformedProfiles: Profile[] = response.data.profiles.map(profile => ({
        name: {
          firstName: profile.name.firstName,
          lastName: profile.name.lastName
        },
        address: {
          line1: profile.address.line1,
          city: profile.address.city,
          countrySubdivisionCode: profile.address.countrySubdivisionCode,
          countryCode: profile.address.countryCode,
          postalCode: profile.address.postalCode
        },
        mobilePhone: {
          number: profile.mobilePhone.number,
          countryDialCode: profile.mobilePhone.countryDialCode
        },
        rewardsNumber: profile.rewardsNumber,
        dateofBirth: profile.dateofBirth,
        profileId: profile.profileId
      }));

      const finalResponse = {
        profiles: transformedProfiles,
        paginationMetadata: {
          totalItems: response.data.paginationMetadata.totalItems,
          currentPage: response.data.paginationMetadata.currentPage,
          perPage: response.data.paginationMetadata.perPage,
          totalPages: response.data.paginationMetadata.totalPages
        }
      };

      // Enhanced debugging - Final transformed response
      console.log('=== MONEYGRAM CONSUMER SEARCH - TRANSFORMED RESPONSE DEBUG ===');
      console.log('Timestamp:', new Date().toISOString());
      console.log('Transformed Response:', JSON.stringify(finalResponse, null, 2));
      console.log('=== END TRANSFORMED RESPONSE DEBUG ===\n');

      return finalResponse;

    } catch (error: any) {
      // Enhanced debugging - Complete error details
      console.log('=== MONEYGRAM CONSUMER SEARCH - ERROR DEBUG ===');
      console.log('Timestamp:', new Date().toISOString());
      console.log('Error Type:', error.constructor.name);
      console.log('Error Message:', error.message);
      console.log('Error Stack:', error.stack);

      if (error.response) {
        console.log('Response Status:', error.response.status);
        console.log('Response Status Text:', error.response.statusText);
        console.log('Response Headers:', JSON.stringify(error.response.headers, null, 2));
        console.log('Response Data:', JSON.stringify(error.response.data, null, 2));
      } else {
        console.log('No response received - likely network error');
      }

      if (error.config) {
        console.log('Request Config:', {
          url: error.config.url,
          method: error.config.method,
          baseURL: error.config.baseURL,
          headers: error.config.headers,
          params: error.config.params,
          timeout: error.config.timeout
        });
      }
      console.log('=== END ERROR DEBUG ===\n');

      logger.error('=== MoneyGram Consumer Search Error ===', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        message: error.message,
        response: error.response?.data,
        headers: error.response?.headers,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          baseURL: error.config?.baseURL,
          params: error.config?.params
        },
        timestamp: new Date().toISOString()
      });

      throw this.handleError(error);
    }
  }

  private normalizeIdentificationType(identificationType: string): string {
    // Normalize identification type to match MoneyGram API expectations
    const normalizedType = identificationType.toUpperCase().replace(/[_-]/g, ' ');

    // Map common variations to MoneyGram expected format
    const typeMapping: { [key: string]: string } = {
      'DRIVER_LICENSE': 'Drivers License',
      'DRIVERS_LICENSE': 'Drivers License',
      'DRIVER LICENSE': 'Drivers License',
      'DRIVERS LICENSE': 'Drivers License',
      'PASSPORT': 'Passport',
      'NATIONAL_ID': 'National ID',
      'NATIONAL ID': 'National ID',
      'STATE_ID': 'State ID',
      'STATE ID': 'State ID'
    };

    return typeMapping[normalizedType] || identificationType;
  }

  private handleError(error: any): any {
    // Handle network errors or cases where response is undefined
    if (!error.response) {
      return {
        error: {
          category: 'NETWORK_ERROR',
          code: 'CONNECTION_ERROR',
          message: error.message || 'Network error occurred while searching consumer profiles',
          offendingFields: []
        }
      };
    }

    // Transform to the desired format
    return {
      error: {
        category: error.response.status ? 'API_ERROR' : 'SYSTEM_ERROR',
        code: String(error.response.status || 'UNKNOWN'),
        message: error.response.data?.message || error.message || 'Failed to search consumer profiles',
        offendingFields: error.response.data?.offendingFields?.map((field: any) => ({
          field: field.field || field
        })) || []
      }
    };
  }
}

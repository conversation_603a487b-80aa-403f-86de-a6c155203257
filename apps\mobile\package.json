{"name": "mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "dev": "expo start --dev-client", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all", "build:dev:android": "eas build --platform android --profile development", "build:dev:ios": "eas build --platform ios --profile development", "lint": "echo 'No linting configured yet'", "test": "echo 'No tests configured yet'", "clean": "expo r -c"}, "dependencies": {"expo": "~53.0.9", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eas-cli": "^16.6.2", "typescript": "~5.8.3"}, "private": true}